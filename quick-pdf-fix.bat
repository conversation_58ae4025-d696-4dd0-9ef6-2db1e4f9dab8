@echo off
chcp 65001 >nul
title إصلاح سريع لمشاكل PDF - تصفية برو
color 0A

echo.
echo ⚡ إصلاح سريع لمشاكل PDF - تصفية برو
echo ==========================================
echo.

REM التحقق من وجود Node.js
echo 🔍 فحص Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo 💡 حمّل من: https://nodejs.org
    echo.
    pause
    exit /b 1
)
echo ✅ Node.js متوفر

REM التحقق من وجود npm
echo 🔍 فحص npm...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    pause
    exit /b 1
)
echo ✅ npm متوفر

REM التحقق من وجود package.json
echo 🔍 فحص ملفات المشروع...
if not exist "package.json" (
    echo ❌ ملف package.json غير موجود
    echo 💡 تأكد من تشغيل هذا الملف في مجلد المشروع
    pause
    exit /b 1
)
echo ✅ ملفات المشروع موجودة

echo.
echo 🚀 بدء الإصلاح السريع...
echo.

REM الخطوة 1: تثبيت/إعادة تثبيت Puppeteer
echo 📦 الخطوة 1: تثبيت Puppeteer...
npm install puppeteer --save
if %errorlevel% neq 0 (
    echo ⚠️ فشل في تثبيت Puppeteer، محاولة بديلة...
    npm install puppeteer --force
    if %errorlevel% neq 0 (
        echo ❌ فشل في تثبيت Puppeteer
        goto :error_handling
    )
)
echo ✅ تم تثبيت Puppeteer

REM الخطوة 2: تحميل Chromium
echo 🌐 الخطوة 2: تحميل Chromium...
npx puppeteer browsers install chrome
if %errorlevel% neq 0 (
    echo ⚠️ فشل في تحميل Chromium عبر Puppeteer
    echo 💡 سيتم البحث عن متصفح مثبت...
) else (
    echo ✅ تم تحميل Chromium
)

REM الخطوة 3: البحث عن متصفحات مثبتة
echo 🔍 الخطوة 3: البحث عن متصفحات مثبتة...
set BROWSER_FOUND=0

if exist "C:\Program Files\Google\Chrome\Application\chrome.exe" (
    echo ✅ تم العثور على Google Chrome
    set BROWSER_FOUND=1
)

if exist "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" (
    echo ✅ تم العثور على Google Chrome ^(x86^)
    set BROWSER_FOUND=1
)

if exist "C:\Program Files\Microsoft\Edge\Application\msedge.exe" (
    echo ✅ تم العثور على Microsoft Edge
    set BROWSER_FOUND=1
)

if exist "C:\Program Files (x86)\Microsoft\Edge\Application\msedge.exe" (
    echo ✅ تم العثور على Microsoft Edge ^(x86^)
    set BROWSER_FOUND=1
)

if %BROWSER_FOUND%==0 (
    echo ⚠️ لم يتم العثور على متصفح مثبت
    echo 💡 يُنصح بتثبيت Google Chrome من: https://www.google.com/chrome/
    echo 💡 أو سيتم استخدام Chromium المحمل عبر Puppeteer
)

REM الخطوة 4: اختبار PDF المتقدم
echo 🧪 الخطوة 4: اختبار PDF المتقدم...
if exist "test-pdf-advanced.js" (
    echo 🔄 تشغيل الاختبار المتقدم...
    node test-pdf-advanced.js
    if %errorlevel% equ 0 (
        echo ✅ نجح الاختبار المتقدم!
        goto :success
    ) else (
        echo ⚠️ فشل الاختبار المتقدم، محاولة اختبار بسيط...
    )
) else (
    echo ℹ️ ملف الاختبار المتقدم غير موجود، اختبار بسيط...
)

REM اختبار بسيط
echo 🔄 اختبار PDF بسيط...
node -e "
const puppeteer = require('puppeteer');
(async () => {
  try {
    console.log('🚀 بدء اختبار PDF...');
    
    const browser = await puppeteer.launch({
      headless: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu'
      ]
    });
    
    console.log('✅ تم تشغيل المتصفح');
    
    const page = await browser.newPage();
    await page.setContent('<h1>اختبار PDF</h1><p>تم بنجاح!</p>');
    
    console.log('✅ تم تحميل المحتوى');
    
    const pdf = await page.pdf({ format: 'A4' });
    
    console.log('✅ تم إنشاء PDF - الحجم:', pdf.length, 'بايت');
    
    await browser.close();
    console.log('🎉 جميع الاختبارات نجحت!');
    
    process.exit(0);
  } catch (error) {
    console.log('❌ فشل الاختبار:', error.message);
    process.exit(1);
  }
})();
"

if %errorlevel% equ 0 (
    goto :success
) else (
    goto :error_handling
)

:success
echo.
echo 🎉 تم إصلاح مشاكل PDF بنجاح!
echo ==========================================
echo ✅ Puppeteer مثبت ويعمل
echo ✅ متصفح متوافق متاح
echo ✅ اختبار PDF نجح
echo.
echo 💡 نصائح للاستخدام:
echo    - يمكنك الآن استخدام تصدير PDF في التطبيق
echo    - في حالة استمرار المشاكل، استخدم التطبيق المُصدَّر
echo    - تأكد من وجود اتصال إنترنت عند التشغيل الأول
echo.
goto :end

:error_handling
echo.
echo ❌ لا تزال هناك مشاكل في PDF
echo ==========================================
echo.
echo 🔧 حلول بديلة:
echo.
echo 1️⃣ تثبيت Google Chrome يدوياً:
echo    https://www.google.com/chrome/
echo.
echo 2️⃣ استخدام التطبيق المُصدَّر:
echo    dist\نظام تصفية الكاشير Setup 1.0.0.exe
echo.
echo 3️⃣ تشغيل التطبيق كمسؤول:
echo    انقر بالزر الأيمن واختر "Run as administrator"
echo.
echo 4️⃣ تعطيل مكافح الفيروسات مؤقتاً
echo.
echo 5️⃣ إعادة تشغيل الكمبيوتر
echo.
echo 6️⃣ التحقق من مساحة القرص الصلب ^(2GB على الأقل^)
echo.

:end
echo.
pause
