/**
 * اختبار متقدم لوظائف PDF
 * يختبر جميع التحسينات الجديدة في pdf-generator.js
 */

const PDFGenerator = require('./src/pdf-generator');

async function runAdvancedPDFTests() {
    console.log('🚀 بدء الاختبار المتقدم لوظائف PDF');
    console.log('='.repeat(50));
    
    const pdfGenerator = new PDFGenerator();
    
    try {
        // الاختبار 1: التشخيص الشامل
        console.log('\n🔍 الاختبار 1: التشخيص الشامل');
        console.log('-'.repeat(30));
        const diagnostics = await pdfGenerator.diagnose();
        
        // الاختبار 2: تشغيل الاختبارات الشاملة
        console.log('\n🧪 الاختبار 2: الاختبارات الشاملة');
        console.log('-'.repeat(30));
        const testResults = await pdfGenerator.runTests();
        
        // الاختبار 3: اختبار التهيئة المحسنة
        console.log('\n⚙️ الاختبار 3: التهيئة المحسنة');
        console.log('-'.repeat(30));
        const initResult = await pdfGenerator.initialize();
        console.log(`نتيجة التهيئة: ${initResult ? '✅ نجح' : '❌ فشل'}`);
        
        if (initResult) {
            // الاختبار 4: إنشاء PDF تجريبي
            console.log('\n📄 الاختبار 4: إنشاء PDF تجريبي');
            console.log('-'.repeat(30));
            
            const testHTML = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>اختبار PDF متقدم</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        direction: rtl;
                        margin: 20px;
                    }
                    .header {
                        text-align: center;
                        color: #2c3e50;
                        border-bottom: 2px solid #3498db;
                        padding-bottom: 10px;
                        margin-bottom: 20px;
                    }
                    .content {
                        line-height: 1.6;
                    }
                    .success {
                        background: #d4edda;
                        color: #155724;
                        padding: 10px;
                        border-radius: 5px;
                        margin: 10px 0;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <h1>🎉 اختبار PDF متقدم</h1>
                    <p>تم إنشاء هذا PDF باستخدام التحسينات الجديدة</p>
                </div>
                
                <div class="content">
                    <h2>📋 معلومات النظام:</h2>
                    <ul>
                        <li><strong>Node.js:</strong> ${process.version}</li>
                        <li><strong>النظام:</strong> ${process.platform}</li>
                        <li><strong>المعمارية:</strong> ${process.arch}</li>
                        <li><strong>التاريخ:</strong> ${new Date().toLocaleDateString('ar-SA')}</li>
                        <li><strong>الوقت:</strong> ${new Date().toLocaleTimeString('ar-SA')}</li>
                    </ul>
                    
                    <div class="success">
                        <h3>✅ نجح الاختبار!</h3>
                        <p>تم إنشاء PDF بنجاح باستخدام التحسينات الجديدة في pdf-generator.js</p>
                    </div>
                    
                    <h2>🔧 التحسينات المطبقة:</h2>
                    <ol>
                        <li>البحث الشامل عن المتصفحات المتوافقة</li>
                        <li>محاولات متعددة للتهيئة مع خيارات مختلفة</li>
                        <li>إصلاح تلقائي متقدم</li>
                        <li>تشخيص شامل للمشاكل</li>
                        <li>اختبارات شاملة للوظائف</li>
                        <li>معالجة أخطاء محسنة</li>
                        <li>نصائح استكشاف الأخطاء</li>
                    </ol>
                    
                    <h2>📊 نتائج التشخيص:</h2>
                    <ul>
                        <li><strong>Puppeteer مثبت:</strong> ${diagnostics.puppeteerInstalled ? '✅' : '❌'}</li>
                        <li><strong>متصفح موجود:</strong> ${diagnostics.chromiumFound ? '✅' : '❌'}</li>
                        <li><strong>مسار المتصفح:</strong> ${diagnostics.chromiumPath || 'غير محدد'}</li>
                        <li><strong>استخدام الذاكرة:</strong> ${Math.round(diagnostics.memoryUsage.heapUsed / 1024 / 1024)} MB</li>
                    </ul>
                    
                    <h2>🧪 نتائج الاختبارات:</h2>
                    <ul>
                        ${testResults.map(result => 
                            `<li><strong>${result.name}:</strong> ${result.status === 'نجح' ? '✅' : '❌'} ${result.status}</li>`
                        ).join('')}
                    </ul>
                </div>
            </body>
            </html>
            `;
            
            try {
                const pdfBuffer = await pdfGenerator.generateFromHTML(testHTML);
                
                if (pdfBuffer && pdfBuffer.length > 0) {
                    // حفظ PDF للاختبار
                    const fs = require('fs');
                    const path = require('path');
                    const outputPath = path.join(__dirname, 'test-pdf-advanced-output.pdf');
                    
                    fs.writeFileSync(outputPath, pdfBuffer);
                    console.log(`✅ تم إنشاء PDF بنجاح!`);
                    console.log(`📁 المسار: ${outputPath}`);
                    console.log(`📊 الحجم: ${(pdfBuffer.length / 1024).toFixed(2)} KB`);
                    
                    // فتح PDF في المتصفح الافتراضي
                    const { exec } = require('child_process');
                    exec(`start "" "${outputPath}"`, (error) => {
                        if (error) {
                            console.log('ℹ️ لم يتم فتح PDF تلقائياً، يمكنك فتحه يدوياً');
                        } else {
                            console.log('🌐 تم فتح PDF في المتصفح الافتراضي');
                        }
                    });
                } else {
                    console.log('❌ فشل في إنشاء PDF - البيانات فارغة');
                }
            } catch (pdfError) {
                console.log('❌ فشل في إنشاء PDF:', pdfError.message);
            }
            
            // إغلاق المتصفح
            await pdfGenerator.close();
        }
        
        // النتيجة النهائية
        console.log('\n🎯 النتيجة النهائية');
        console.log('='.repeat(50));
        
        const passedTests = testResults.filter(r => r.status === 'نجح').length;
        const totalTests = testResults.length;
        const successRate = Math.round((passedTests / totalTests) * 100);
        
        console.log(`📊 معدل النجاح: ${successRate}% (${passedTests}/${totalTests})`);
        console.log(`🔧 التشخيص: ${diagnostics.puppeteerInstalled && diagnostics.chromiumFound ? '✅ سليم' : '⚠️ يحتاج إصلاح'}`);
        console.log(`⚙️ التهيئة: ${initResult ? '✅ نجحت' : '❌ فشلت'}`);
        
        if (successRate >= 80 && initResult) {
            console.log('\n🎉 جميع الاختبارات نجحت! PDF جاهز للاستخدام');
        } else {
            console.log('\n⚠️ بعض الاختبارات فشلت. راجع الأخطاء أعلاه');
            console.log('\n💡 نصائح للإصلاح:');
            console.log('   1. تشغيل: npm install puppeteer');
            console.log('   2. تشغيل: npx puppeteer browsers install chrome');
            console.log('   3. تثبيت Google Chrome');
            console.log('   4. إعادة تشغيل التطبيق كمسؤول');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار المتقدم:', error.message);
        console.error('📋 تفاصيل الخطأ:', error.stack);
    }
    
    console.log('\n' + '='.repeat(50));
    console.log('🏁 انتهى الاختبار المتقدم');
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
    runAdvancedPDFTests().catch(console.error);
}

module.exports = { runAdvancedPDFTests };
