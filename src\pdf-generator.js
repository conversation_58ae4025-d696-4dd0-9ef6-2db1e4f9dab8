// ===================================================
// 🧾 تطبيق: تصفية برو
// 🛠️ المطور: محمد أمين الكامل
// 🗓️ سنة: 2025
// 📌 جميع الحقوق محفوظة
// يمنع الاستخدام أو التعديل دون إذن كتابي
// ===================================================

const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

class PDFGenerator {
    constructor(dbManager = null) {
        this.browser = null;
        this.dbManager = dbManager;
    }

    async initialize() {
        console.log('🚀 [PDF] بدء تهيئة مولد PDF...');

        // محاولات متعددة للتهيئة
        const maxAttempts = 3;
        let lastError = null;

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                console.log(`🔄 [PDF] محاولة التهيئة ${attempt}/${maxAttempts}...`);

                // البحث عن متصفح متوافق
                const executablePath = this.findChromiumExecutable();

                // إعداد خيارات التشغيل المحسنة
                const launchOptions = this.createLaunchOptions(executablePath, attempt);

                console.log('⚙️ [PDF] خيارات التشغيل:', {
                    executablePath: executablePath || 'افتراضي',
                    headless: launchOptions.headless,
                    argsCount: launchOptions.args.length
                });

                // محاولة تشغيل المتصفح
                this.browser = await puppeteer.launch(launchOptions);

                // اختبار سريع للتأكد من عمل المتصفح
                await this.testBrowser();

                console.log('✅ [PDF] تم تهيئة مولد PDF بنجاح');
                return true;

            } catch (error) {
                lastError = error;
                console.log(`⚠️ [PDF] فشلت محاولة التهيئة ${attempt}:`, error.message);

                // إغلاق المتصفح إذا كان مفتوحاً
                if (this.browser) {
                    try {
                        await this.browser.close();
                    } catch (closeError) {
                        // تجاهل أخطاء الإغلاق
                    }
                    this.browser = null;
                }

                // محاولة إصلاح تلقائي في المحاولة الأولى فقط
                if (attempt === 1) {
                    console.log('🔧 [PDF] محاولة إصلاح تلقائي...');
                    const fixed = await this.attemptAutoFix();
                    if (fixed) {
                        console.log('✅ [PDF] تم الإصلاح، إعادة المحاولة...');
                        continue;
                    }
                }

                // انتظار قبل المحاولة التالية
                if (attempt < maxAttempts) {
                    const delay = attempt * 2000; // زيادة التأخير مع كل محاولة
                    console.log(`⏳ [PDF] انتظار ${delay/1000} ثانية قبل المحاولة التالية...`);
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }

        console.error('❌ [PDF] فشلت جميع محاولات التهيئة');
        console.error('❌ [PDF] آخر خطأ:', lastError?.message);

        // عرض نصائح للمستخدم
        this.showTroubleshootingTips();

        return false;
    }

    /**
     * إنشاء خيارات التشغيل المحسنة
     */
    createLaunchOptions(executablePath, attempt) {
        const baseArgs = [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security'
        ];

        // إضافة خيارات إضافية حسب المحاولة
        if (attempt === 2) {
            baseArgs.push(
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows'
            );
        } else if (attempt === 3) {
            baseArgs.push(
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images',
                '--disable-javascript',
                '--single-process'
            );
        }

        const options = {
            headless: attempt === 3 ? 'new' : true, // استخدام وضع headless الجديد في المحاولة الأخيرة
            args: baseArgs,
            timeout: 30000 + (attempt * 10000), // زيادة المهلة الزمنية
            ignoreDefaultArgs: attempt === 3 ? ['--disable-extensions'] : false
        };

        // إضافة مسار المتصفح إذا تم العثور عليه
        if (executablePath) {
            options.executablePath = executablePath;
        }

        return options;
    }

    /**
     * اختبار سريع للمتصفح
     */
    async testBrowser() {
        if (!this.browser) {
            throw new Error('المتصفح غير متاح');
        }

        const page = await this.browser.newPage();
        await page.setContent('<h1>اختبار</h1>');
        await page.close();

        console.log('✅ [PDF] اختبار المتصفح نجح');
    }

    /**
     * عرض نصائح استكشاف الأخطاء
     */
    showTroubleshootingTips() {
        console.log('\n💡 [PDF] نصائح لحل المشكلة:');
        console.log('   1. تثبيت Google Chrome من: https://www.google.com/chrome/');
        console.log('   2. تشغيل الأمر: npx puppeteer browsers install chrome');
        console.log('   3. إعادة تشغيل التطبيق كمسؤول');
        console.log('   4. التأكد من وجود مساحة كافية على القرص الصلب');
        console.log('   5. تعطيل مكافح الفيروسات مؤقتاً');
        console.log('   6. استخدام التطبيق المُصدَّر بدلاً من الكود المصدري\n');
    }

    /**
     * البحث عن متصفح قابل للتنفيذ مع فحص شامل
     */
    findChromiumExecutable() {
        const fs = require('fs');
        const path = require('path');
        const os = require('os');

        console.log('🔍 [PDF] البحث عن متصفح متوافق...');

        // مسارات المتصفحات المختلفة
        const browserPaths = [
            // Google Chrome
            'C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe',
            'C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe',

            // Microsoft Edge
            'C:\\Program Files (x86)\\Microsoft\\Edge\\Application\\msedge.exe',
            'C:\\Program Files\\Microsoft\\Edge\\Application\\msedge.exe',

            // Chromium
            'C:\\Program Files\\Chromium\\Application\\chromium.exe',
            'C:\\Program Files (x86)\\Chromium\\Application\\chromium.exe',

            // Brave Browser
            'C:\\Program Files\\BraveSoftware\\Brave-Browser\\Application\\brave.exe',
            'C:\\Program Files (x86)\\BraveSoftware\\Brave-Browser\\Application\\brave.exe',

            // Opera
            'C:\\Program Files\\Opera\\opera.exe',
            'C:\\Program Files (x86)\\Opera\\opera.exe',

            // Vivaldi
            'C:\\Program Files\\Vivaldi\\Application\\vivaldi.exe',
            'C:\\Program Files (x86)\\Vivaldi\\Application\\vivaldi.exe',

            // Chrome في مجلد المستخدم
            path.join(os.homedir(), 'AppData\\Local\\Google\\Chrome\\Application\\chrome.exe'),

            // Edge في مجلد المستخدم
            path.join(os.homedir(), 'AppData\\Local\\Microsoft\\Edge\\Application\\msedge.exe')
        ];

        // البحث في المسارات
        for (const browserPath of browserPaths) {
            try {
                if (fs.existsSync(browserPath)) {
                    // التحقق من أن الملف قابل للتنفيذ
                    const stats = fs.statSync(browserPath);
                    if (stats.isFile()) {
                        console.log(`✅ [PDF] تم العثور على متصفح: ${browserPath}`);
                        return browserPath;
                    }
                }
            } catch (error) {
                // تجاهل الأخطاء والمتابعة
                continue;
            }
        }

        // البحث في متغيرات البيئة
        const envPath = process.env.PUPPETEER_EXECUTABLE_PATH;
        if (envPath && fs.existsSync(envPath)) {
            console.log(`✅ [PDF] استخدام متصفح من متغير البيئة: ${envPath}`);
            return envPath;
        }

        // البحث في مجلد Puppeteer
        try {
            const puppeteerChrome = this.findPuppeteerChrome();
            if (puppeteerChrome) {
                console.log(`✅ [PDF] استخدام Chromium من Puppeteer: ${puppeteerChrome}`);
                return puppeteerChrome;
            }
        } catch (error) {
            console.log('⚠️ [PDF] لم يتم العثور على Chromium في Puppeteer');
        }

        console.log('⚠️ [PDF] لم يتم العثور على أي متصفح، سيتم المحاولة بدون مسار محدد');
        return null;
    }

    /**
     * البحث عن Chromium في مجلد Puppeteer
     */
    findPuppeteerChrome() {
        const fs = require('fs');
        const path = require('path');

        try {
            // مسار Chromium في Puppeteer
            const puppeteerPath = path.join(__dirname, '..', 'node_modules', 'puppeteer');
            const chromePath = path.join(puppeteerPath, '.local-chromium');

            if (fs.existsSync(chromePath)) {
                // البحث في مجلدات الإصدارات
                const versions = fs.readdirSync(chromePath);
                for (const version of versions) {
                    const chromiumExe = path.join(chromePath, version, 'chrome-win', 'chrome.exe');
                    if (fs.existsSync(chromiumExe)) {
                        return chromiumExe;
                    }
                }
            }

            // البحث في المسار الجديد لـ Puppeteer
            const newChromePath = path.join(puppeteerPath, '.cache', 'puppeteer');
            if (fs.existsSync(newChromePath)) {
                const platforms = fs.readdirSync(newChromePath);
                for (const platform of platforms) {
                    const platformPath = path.join(newChromePath, platform);
                    if (fs.existsSync(platformPath)) {
                        const versions = fs.readdirSync(platformPath);
                        for (const version of versions) {
                            const chromiumExe = path.join(platformPath, version, 'chrome-win', 'chrome.exe');
                            if (fs.existsSync(chromiumExe)) {
                                return chromiumExe;
                            }
                        }
                    }
                }
            }
        } catch (error) {
            console.log('⚠️ [PDF] خطأ في البحث عن Puppeteer Chrome:', error.message);
        }

        return null;
    }

    /**
     * محاولة إصلاح تلقائي متقدم
     */
    async attemptAutoFix() {
        console.log('🔧 [PDF] بدء الإصلاح التلقائي المتقدم...');

        const fixes = [
            () => this.downloadChromium(),
            () => this.installPuppeteer(),
            () => this.clearPuppeteerCache(),
            () => this.tryAlternativeLaunch()
        ];

        for (let i = 0; i < fixes.length; i++) {
            try {
                console.log(`🔄 [PDF] محاولة الإصلاح ${i + 1}/${fixes.length}...`);
                const success = await fixes[i]();
                if (success) {
                    console.log(`✅ [PDF] نجح الإصلاح ${i + 1}`);
                    return true;
                }
            } catch (error) {
                console.log(`⚠️ [PDF] فشل الإصلاح ${i + 1}:`, error.message);
                continue;
            }
        }

        console.log('❌ [PDF] فشلت جميع محاولات الإصلاح التلقائي');
        return false;
    }

    /**
     * تحميل Chromium
     */
    async downloadChromium() {
        try {
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);

            console.log('📥 [PDF] تحميل Chromium عبر Puppeteer...');

            // محاولة تحميل Chrome
            await execAsync('npx puppeteer browsers install chrome', { timeout: 300000 }); // 5 دقائق
            console.log('✅ [PDF] تم تحميل Chromium بنجاح');

            return true;
        } catch (error) {
            console.log('❌ [PDF] فشل في تحميل Chromium:', error.message);
            return false;
        }
    }

    /**
     * إعادة تثبيت Puppeteer
     */
    async installPuppeteer() {
        try {
            const { exec } = require('child_process');
            const { promisify } = require('util');
            const execAsync = promisify(exec);

            console.log('📦 [PDF] إعادة تثبيت Puppeteer...');
            await execAsync('npm install puppeteer --force', { timeout: 180000 }); // 3 دقائق
            console.log('✅ [PDF] تم إعادة تثبيت Puppeteer');

            return true;
        } catch (error) {
            console.log('❌ [PDF] فشل في إعادة تثبيت Puppeteer:', error.message);
            return false;
        }
    }

    /**
     * مسح ذاكرة التخزين المؤقت لـ Puppeteer
     */
    async clearPuppeteerCache() {
        try {
            const fs = require('fs');
            const path = require('path');

            console.log('🧹 [PDF] مسح ذاكرة التخزين المؤقت...');

            const cachePaths = [
                path.join(__dirname, '..', 'node_modules', 'puppeteer', '.local-chromium'),
                path.join(__dirname, '..', 'node_modules', 'puppeteer', '.cache'),
                path.join(require('os').homedir(), '.cache', 'puppeteer')
            ];

            for (const cachePath of cachePaths) {
                if (fs.existsSync(cachePath)) {
                    fs.rmSync(cachePath, { recursive: true, force: true });
                    console.log(`🗑️ [PDF] تم مسح: ${cachePath}`);
                }
            }

            console.log('✅ [PDF] تم مسح ذاكرة التخزين المؤقت');
            return true;
        } catch (error) {
            console.log('❌ [PDF] فشل في مسح ذاكرة التخزين المؤقت:', error.message);
            return false;
        }
    }

    /**
     * محاولة تشغيل بديل
     */
    async tryAlternativeLaunch() {
        try {
            console.log('🔄 [PDF] محاولة تشغيل بديل...');

            // خيارات تشغيل بديلة
            const alternativeOptions = {
                headless: 'new', // استخدام وضع headless الجديد
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding',
                    '--disable-field-trial-config',
                    '--disable-ipc-flooding-protection'
                ],
                ignoreDefaultArgs: ['--disable-extensions'],
                timeout: 30000
            };

            const browser = await puppeteer.launch(alternativeOptions);
            await browser.close();

            console.log('✅ [PDF] نجح التشغيل البديل');
            return true;
        } catch (error) {
            console.log('❌ [PDF] فشل التشغيل البديل:', error.message);
            return false;
        }
    }

    async generateReconciliationReport(reconciliationData) {
        if (!this.browser) {
            const initialized = await this.initialize();
            if (!initialized) {
                throw new Error('Failed to initialize PDF generator');
            }
        }

        try {
            const page = await this.browser.newPage();
            
            // Set page to A4 size
            await page.setViewport({ width: 794, height: 1123 });
            
            // Generate HTML content
            const htmlContent = await this.generateReportHTML(reconciliationData);
            
            // Set content
            await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
            
            // Generate PDF with footer on every page
            const pdfBuffer = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20mm',
                    right: '15mm',
                    bottom: '25mm', // زيادة الهامش السفلي لاستيعاب الفوتر
                    left: '15mm'
                },
                displayHeaderFooter: true,
                footerTemplate: `
                    <div style="font-size: 10px; color: #666; text-align: center; width: 100%; padding: 5px 0; font-family: 'Cairo', Arial, sans-serif;">
                        جميع الحقوق محفوظة © 2025 - تطوير محمد أمين الكامل - نظام تصفية برو
                    </div>
                `,
                headerTemplate: '<div></div>' // فوتر فارغ
            });
            
            await page.close();
            
            return pdfBuffer;
            
        } catch (error) {
            console.error('Error generating PDF:', error);
            throw error;
        }
    }

    async generateFromHTML(htmlContent) {
        if (!this.browser) {
            const initialized = await this.initialize();
            if (!initialized) {
                throw new Error('Failed to initialize PDF generator');
            }
        }

        try {
            const page = await this.browser.newPage();

            // Set page to A4 size
            await page.setViewport({ width: 794, height: 1123 });

            // Set content
            await page.setContent(htmlContent, { waitUntil: 'networkidle0' });

            // Generate PDF with footer on every page
            const pdfBuffer = await page.pdf({
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20mm',
                    right: '15mm',
                    bottom: '25mm', // زيادة الهامش السفلي لاستيعاب الفوتر
                    left: '15mm'
                },
                displayHeaderFooter: true,
                footerTemplate: `
                    <div style="font-size: 10px; color: #666; text-align: center; width: 100%; padding: 5px 0; font-family: 'Cairo', Arial, sans-serif;">
                        جميع الحقوق محفوظة © 2025 - تطوير محمد أمين الكامل - نظام تصفية برو
                    </div>
                `,
                headerTemplate: '<div></div>' // هيدر فارغ
            });

            await page.close();

            return pdfBuffer;

        } catch (error) {
            console.error('PDF generation from HTML error:', error);
            throw error;
        }
    }

    async generateReportHTML(data) {
        const currentDate = new Date().toLocaleDateString('ar-SA');
        const currentTime = new Date().toLocaleTimeString('ar-SA');

        // Get company name from database
        let companyName = 'تقرير النظام';
        try {
            const result = await this.dbManager.get(
                'SELECT setting_value FROM system_settings WHERE category = ? AND setting_key = ?',
                ['general', 'company_name']
            );
            if (result && result.setting_value) {
                companyName = result.setting_value;
            }
        } catch (error) {
            console.log('ℹ️ [PDF] استخدام اسم افتراضي للشركة');
        }

        return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>تقرير تصفية الكاشير - ${companyName}</title>
            <style>
                @import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');

                * {
                    font-family: 'Cairo', 'Arial', sans-serif;
                    margin: 0;
                    padding: 0;
                    box-sizing: border-box;
                }

                body {
                    direction: rtl;
                    text-align: right;
                    font-size: 14px;
                    line-height: 1.6;
                    color: #333;
                    background: white;
                }

                .company-header {
                    text-align: center;
                    margin-bottom: 20px;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    border-radius: 10px;
                }

                .company-name {
                    font-size: 28px;
                    font-weight: 700;
                    margin-bottom: 8px;
                }

                .report-title {
                    font-size: 20px;
                    font-weight: 400;
                    opacity: 0.9;
                }

                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 3px solid #3498db;
                    padding-bottom: 20px;
                }
                
                .header h1 {
                    font-size: 24px;
                    font-weight: 700;
                    color: #2c3e50;
                    margin-bottom: 10px;
                }
                
                .header .company-info {
                    font-size: 16px;
                    color: #7f8c8d;
                    margin-bottom: 15px;
                }
                
                .report-info {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 20px;
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                }
                
                .info-section {
                    flex: 1;
                }
                
                .info-section h3 {
                    font-size: 16px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 10px;
                    border-bottom: 2px solid #3498db;
                    padding-bottom: 5px;
                }
                
                .info-item {
                    margin-bottom: 8px;
                    display: flex;
                    justify-content: space-between;
                }
                
                .info-label {
                    font-weight: 600;
                    color: #34495e;
                }
                
                .info-value {
                    color: #2c3e50;
                }
                
                .section {
                    margin-bottom: 25px;
                }
                
                .section-title {
                    font-size: 18px;
                    font-weight: 600;
                    color: #2c3e50;
                    margin-bottom: 15px;
                    background: linear-gradient(135deg, #3498db, #2980b9);
                    color: white;
                    padding: 10px 15px;
                    border-radius: 8px;
                }
                
                .table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 15px;
                    background: white;
                    border-radius: 8px;
                    overflow: hidden;
                    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
                }
                
                .table th {
                    background: #34495e;
                    color: white;
                    padding: 12px;
                    text-align: center;
                    font-weight: 600;
                }
                
                .table td {
                    padding: 10px 12px;
                    border-bottom: 1px solid #ecf0f1;
                    text-align: center;
                }
                
                .table tbody tr:nth-child(even) {
                    background: #f8f9fa;
                }
                
                .table tfoot td {
                    background: #3498db;
                    color: white;
                    font-weight: 600;
                    font-size: 16px;
                }
                
                .summary-grid {
                    display: grid;
                    grid-template-columns: 1fr 1fr;
                    gap: 20px;
                    margin-bottom: 20px;
                }
                
                .summary-box {
                    background: #f8f9fa;
                    padding: 15px;
                    border-radius: 8px;
                    border-right: 4px solid #3498db;
                }
                
                .summary-item {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 10px;
                    padding: 8px 0;
                    border-bottom: 1px solid #ecf0f1;
                }
                
                .summary-label {
                    font-weight: 600;
                    color: #2c3e50;
                }
                
                .summary-value {
                    font-weight: 700;
                    color: #27ae60;
                    font-family: 'Courier New', monospace;
                }
                
                .final-result {
                    background: linear-gradient(135deg, #2c3e50, #34495e);
                    color: white;
                    padding: 20px;
                    border-radius: 10px;
                    text-align: center;
                    margin-top: 20px;
                }
                
                .final-result h2 {
                    font-size: 20px;
                    margin-bottom: 10px;
                }
                
                .final-amount {
                    font-size: 24px;
                    font-weight: 700;
                    font-family: 'Courier New', monospace;
                }
                
                .surplus {
                    color: #2ecc71;
                }
                
                .deficit {
                    color: #e74c3c;
                }
                
                .balanced {
                    color: #3498db;
                }
                
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    font-size: 12px;
                    color: #7f8c8d;
                    border-top: 1px solid #ecf0f1;
                    padding-top: 15px;
                    margin-bottom: 20px; /* مساحة إضافية لتجنب التداخل مع فوتر الصفحة */
                }

                /* تأكد من أن المحتوى لا يتداخل مع فوتر الصفحة */
                @page {
                    margin-bottom: 25mm;
                }

                /* فوتر الصفحة - يظهر في كل صفحة */
                .page-footer {
                    position: fixed;
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 20mm;
                    background: white;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 10px;
                    color: #666;
                    border-top: 1px solid #ddd;
                }
                
                @media print {
                    body {
                        -webkit-print-color-adjust: exact;
                        print-color-adjust: exact;
                    }
                }
            </style>
        </head>
        <body>
            <div class="company-header">
                <div class="company-name">${companyName}</div>
                <div class="report-title">تقرير تصفية الكاشير</div>
            </div>

            <div class="header">
                <h1>تفاصيل التصفية</h1>
            </div>
            
            <div class="report-info">
                <div class="info-section">
                    <h3>معلومات التصفية</h3>
                    <div class="info-item">
                        <span class="info-label">الكاشير:</span>
                        <span class="info-value">${data.cashierName} (${data.cashierNumber})</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">المحاسب:</span>
                        <span class="info-value">${data.accountantName}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">تاريخ التصفية:</span>
                        <span class="info-value">${data.reconciliationDate}</span>
                    </div>
                </div>
                <div class="info-section">
                    <h3>معلومات التقرير</h3>
                    <div class="info-item">
                        <span class="info-label">تاريخ الطباعة:</span>
                        <span class="info-value">${currentDate}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">وقت الطباعة:</span>
                        <span class="info-value">${currentTime}</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">رقم التصفية:</span>
                        <span class="info-value">#${data.reconciliationId}</span>
                    </div>
                </div>
            </div>
            
            ${this.generateBankReceiptsSection(data.bankReceipts)}
            ${this.generateCashReceiptsSection(data.cashReceipts)}
            ${this.generatePostpaidSalesSection(data.postpaidSales)}
            ${this.generateCustomerReceiptsSection(data.customerReceipts)}
            ${this.generateReturnInvoicesSection(data.returnInvoices)}
            ${this.generateSuppliersSection(data.suppliers)}
            ${this.generateSummarySection(data.summary)}
            
            <div class="footer">
                <p>تم إنشاء هذا التقرير بواسطة نظام تصفية برو</p>
                <p>تاريخ الإنشاء: ${currentDate} - الوقت: ${currentTime}</p>
                <p style="margin-top: 10px; font-weight: 600; color: #2c3e50;">
                    جميع الحقوق محفوظة © 2025 - تطوير محمد أمين الكامل - نظام تصفية برو
                </p>
            </div>
        </body>
        </html>
        `;
    }

    generateBankReceiptsSection(bankReceipts) {
        if (!bankReceipts || bankReceipts.length === 0) {
            return '<div class="section"><div class="section-title">المقبوضات البنكية</div><p>لا توجد مقبوضات بنكية</p></div>';
        }

        const total = bankReceipts.reduce((sum, receipt) => sum + receipt.amount, 0);
        
        return `
        <div class="section">
            <div class="section-title">المقبوضات البنكية</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>نوع العملية</th>
                        <th>الجهاز</th>
                        <th>البنك</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    ${bankReceipts.map(receipt => `
                        <tr>
                            <td>${receipt.operation_type}</td>
                            <td>${receipt.atm_name}</td>
                            <td>${receipt.bank_name}</td>
                            <td>${receipt.amount.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="3">المجموع</td>
                        <td>${total.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generateCashReceiptsSection(cashReceipts) {
        if (!cashReceipts || cashReceipts.length === 0) {
            return '<div class="section"><div class="section-title">💰 المقبوضات النقدية</div><p>لا توجد مقبوضات نقدية</p></div>';
        }

        // Sort by denomination descending for better readability
        const sortedCashReceipts = [...cashReceipts].sort((a, b) => (b.denomination || 0) - (a.denomination || 0));

        const total = cashReceipts.reduce((sum, receipt) => sum + (receipt.total_amount || 0), 0);
        const totalQuantity = cashReceipts.reduce((sum, receipt) => sum + (receipt.quantity || 0), 0);

        return `
        <div class="section">
            <div class="section-title">💰 المقبوضات النقدية (${cashReceipts.length})</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>الفئة</th>
                        <th>العدد</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${sortedCashReceipts.map(receipt => `
                        <tr>
                            <td>${this.formatNumber(receipt.denomination || 0)} ريال</td>
                            <td>${this.formatNumber(receipt.quantity || 0)}</td>
                            <td>${this.formatNumber((receipt.total_amount || 0).toFixed(2))} ريال</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr style="background: #e8f5e8; font-weight: bold;">
                        <td>الإجمالي</td>
                        <td>${this.formatNumber(totalQuantity)}</td>
                        <td>${this.formatNumber(total.toFixed(2))} ريال</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generatePostpaidSalesSection(postpaidSales) {
        if (!postpaidSales || postpaidSales.length === 0) {
            return '<div class="section"><div class="section-title">المبيعات الآجلة</div><p>لا توجد مبيعات آجلة</p></div>';
        }

        const total = postpaidSales.reduce((sum, sale) => sum + sale.amount, 0);

        return `
        <div class="section">
            <div class="section-title">المبيعات الآجلة</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    ${postpaidSales.map(sale => `
                        <tr>
                            <td>${sale.customer_name}</td>
                            <td>${sale.amount.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td>المجموع</td>
                        <td>${total.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generateCustomerReceiptsSection(customerReceipts) {
        if (!customerReceipts || customerReceipts.length === 0) {
            return '<div class="section"><div class="section-title">مقبوضات العملاء</div><p>لا توجد مقبوضات عملاء</p></div>';
        }

        const total = customerReceipts.reduce((sum, receipt) => sum + receipt.amount, 0);

        return `
        <div class="section">
            <div class="section-title">مقبوضات العملاء</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم العميل</th>
                        <th>المبلغ</th>
                        <th>نوع الدفع</th>
                    </tr>
                </thead>
                <tbody>
                    ${customerReceipts.map(receipt => `
                        <tr>
                            <td>${receipt.customer_name}</td>
                            <td>${receipt.amount.toFixed(2)}</td>
                            <td>${receipt.payment_type}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="2">المجموع</td>
                        <td>${total.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generateReturnInvoicesSection(returnInvoices) {
        if (!returnInvoices || returnInvoices.length === 0) {
            return '<div class="section"><div class="section-title">فواتير المرتجعات</div><p>لا توجد فواتير مرتجعات</p></div>';
        }

        const total = returnInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);

        return `
        <div class="section">
            <div class="section-title">فواتير المرتجعات</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    ${returnInvoices.map(invoice => `
                        <tr>
                            <td>${invoice.invoice_number}</td>
                            <td>${invoice.amount.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td>المجموع</td>
                        <td>${total.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generateSuppliersSection(suppliers) {
        if (!suppliers || suppliers.length === 0) {
            return '<div class="section"><div class="section-title">الموردين (للعرض فقط)</div><p>لا توجد معاملات موردين</p></div>';
        }

        const total = suppliers.reduce((sum, supplier) => sum + supplier.amount, 0);

        return `
        <div class="section">
            <div class="section-title">الموردين (للعرض فقط - لا يؤثر على المجاميع)</div>
            <table class="table">
                <thead>
                    <tr>
                        <th>اسم المورد</th>
                        <th>المبلغ</th>
                    </tr>
                </thead>
                <tbody>
                    ${suppliers.map(supplier => `
                        <tr>
                            <td>${supplier.supplier_name}</td>
                            <td>${supplier.amount.toFixed(2)}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr>
                        <td>المجموع (للعرض فقط)</td>
                        <td>${total.toFixed(2)}</td>
                    </tr>
                </tfoot>
            </table>
        </div>
        `;
    }

    generateSummarySection(summary) {
        const surplusDeficitClass = summary.surplusDeficit > 0 ? 'surplus' :
                                   summary.surplusDeficit < 0 ? 'deficit' : 'balanced';

        const surplusDeficitText = summary.surplusDeficit > 0 ? `فائض: ${summary.surplusDeficit.toFixed(2)}` :
                                  summary.surplusDeficit < 0 ? `عجز: ${Math.abs(summary.surplusDeficit).toFixed(2)}` :
                                  'متوازن: 0.00';

        return `
        <div class="section">
            <div class="section-title">ملخص التصفية</div>
            <div class="summary-grid">
                <div class="summary-box">
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المقبوضات البنكية:</span>
                        <span class="summary-value">${summary.bankTotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المقبوضات النقدية:</span>
                        <span class="summary-value">${summary.cashTotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المبيعات الآجلة:</span>
                        <span class="summary-value">${summary.postpaidTotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي مقبوضات العملاء:</span>
                        <span class="summary-value">${summary.customerTotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المرتجعات:</span>
                        <span class="summary-value">${summary.returnTotal.toFixed(2)}</span>
                    </div>
                </div>
                <div class="summary-box">
                    <div class="summary-item">
                        <span class="summary-label">مبيعات النظام:</span>
                        <span class="summary-value">${summary.systemSales.toFixed(2)}</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">إجمالي المقبوضات:</span>
                        <span class="summary-value">${summary.totalReceipts.toFixed(2)}</span>
                    </div>
                </div>
            </div>

            <div class="final-result">
                <h2>النتيجة النهائية</h2>
                <div class="final-amount ${surplusDeficitClass}">
                    ${surplusDeficitText}
                </div>
            </div>
        </div>
        `;
    }

    async close() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    /**
     * تشخيص شامل لمشاكل PDF
     */
    async diagnose() {
        console.log('🔍 [PDF] بدء التشخيص الشامل...');

        const diagnostics = {
            nodeVersion: process.version,
            platform: process.platform,
            arch: process.arch,
            puppeteerInstalled: false,
            chromiumFound: false,
            chromiumPath: null,
            memoryUsage: process.memoryUsage(),
            diskSpace: null,
            recommendations: []
        };

        try {
            // فحص تثبيت Puppeteer
            require('puppeteer');
            diagnostics.puppeteerInstalled = true;
            console.log('✅ [PDF] Puppeteer مثبت');
        } catch (error) {
            diagnostics.puppeteerInstalled = false;
            diagnostics.recommendations.push('تثبيت Puppeteer: npm install puppeteer');
            console.log('❌ [PDF] Puppeteer غير مثبت');
        }

        // فحص وجود متصفح
        const chromiumPath = this.findChromiumExecutable();
        if (chromiumPath) {
            diagnostics.chromiumFound = true;
            diagnostics.chromiumPath = chromiumPath;
            console.log('✅ [PDF] متصفح متوافق موجود');
        } else {
            diagnostics.chromiumFound = false;
            diagnostics.recommendations.push('تثبيت Google Chrome أو تحميل Chromium');
            console.log('❌ [PDF] لا يوجد متصفح متوافق');
        }

        // فحص مساحة القرص
        try {
            const fs = require('fs');
            const stats = fs.statSync(process.cwd());
            diagnostics.diskSpace = 'متوفرة';
        } catch (error) {
            diagnostics.diskSpace = 'غير متوفرة';
            diagnostics.recommendations.push('التحقق من مساحة القرص الصلب');
        }

        // فحص الذاكرة
        const memoryMB = Math.round(diagnostics.memoryUsage.heapUsed / 1024 / 1024);
        if (memoryMB > 500) {
            diagnostics.recommendations.push('إعادة تشغيل التطبيق لتحرير الذاكرة');
        }

        // عرض النتائج
        console.log('\n📊 [PDF] نتائج التشخيص:');
        console.log('='.repeat(40));
        console.log(`Node.js: ${diagnostics.nodeVersion}`);
        console.log(`النظام: ${diagnostics.platform} ${diagnostics.arch}`);
        console.log(`Puppeteer: ${diagnostics.puppeteerInstalled ? '✅' : '❌'}`);
        console.log(`متصفح: ${diagnostics.chromiumFound ? '✅' : '❌'}`);
        if (diagnostics.chromiumPath) {
            console.log(`مسار المتصفح: ${diagnostics.chromiumPath}`);
        }
        console.log(`استخدام الذاكرة: ${memoryMB} MB`);
        console.log(`مساحة القرص: ${diagnostics.diskSpace}`);

        if (diagnostics.recommendations.length > 0) {
            console.log('\n💡 [PDF] التوصيات:');
            diagnostics.recommendations.forEach((rec, index) => {
                console.log(`${index + 1}. ${rec}`);
            });
        }

        console.log('='.repeat(40));

        return diagnostics;
    }

    /**
     * اختبار شامل لوظائف PDF
     */
    async runTests() {
        console.log('🧪 [PDF] بدء الاختبارات الشاملة...');

        const tests = [
            { name: 'تهيئة المتصفح', test: () => this.testBrowserLaunch() },
            { name: 'إنشاء صفحة', test: () => this.testPageCreation() },
            { name: 'تحميل محتوى HTML', test: () => this.testHTMLLoading() },
            { name: 'إنشاء PDF', test: () => this.testPDFGeneration() },
            { name: 'إغلاق المتصفح', test: () => this.testBrowserClose() }
        ];

        const results = [];

        for (const test of tests) {
            try {
                console.log(`🔄 [PDF] اختبار: ${test.name}...`);
                await test.test();
                results.push({ name: test.name, status: 'نجح', error: null });
                console.log(`✅ [PDF] ${test.name}: نجح`);
            } catch (error) {
                results.push({ name: test.name, status: 'فشل', error: error.message });
                console.log(`❌ [PDF] ${test.name}: فشل - ${error.message}`);
            }
        }

        // عرض النتائج النهائية
        console.log('\n📋 [PDF] نتائج الاختبارات:');
        console.log('='.repeat(40));
        results.forEach(result => {
            const status = result.status === 'نجح' ? '✅' : '❌';
            console.log(`${status} ${result.name}: ${result.status}`);
            if (result.error) {
                console.log(`   الخطأ: ${result.error}`);
            }
        });

        const passedTests = results.filter(r => r.status === 'نجح').length;
        console.log(`\n📊 [PDF] النتيجة: ${passedTests}/${results.length} اختبارات نجحت`);

        return results;
    }

    // اختبارات فردية
    async testBrowserLaunch() {
        const browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        await browser.close();
    }

    async testPageCreation() {
        if (!this.browser) await this.initialize();
        const page = await this.browser.newPage();
        await page.close();
    }

    async testHTMLLoading() {
        if (!this.browser) await this.initialize();
        const page = await this.browser.newPage();
        await page.setContent('<h1>اختبار</h1><p>محتوى تجريبي</p>');
        await page.close();
    }

    async testPDFGeneration() {
        if (!this.browser) await this.initialize();
        const page = await this.browser.newPage();
        await page.setContent('<h1>اختبار PDF</h1>');
        const pdf = await page.pdf({ format: 'A4' });
        await page.close();

        if (!pdf || pdf.length === 0) {
            throw new Error('PDF فارغ');
        }
    }

    async testBrowserClose() {
        if (this.browser) {
            await this.browser.close();
            this.browser = null;
        }
    }

    // Helper method to format numbers using English digits
    formatNumber(number) {
        if (number === null || number === undefined) return '0';

        try {
            return new Intl.NumberFormat('en-US').format(number);
        } catch (error) {
            console.error('Error formatting number:', error);
            return String(number);
        }
    }
}

module.exports = PDFGenerator;
